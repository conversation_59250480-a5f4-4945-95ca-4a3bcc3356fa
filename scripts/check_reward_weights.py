#!/usr/bin/env python3

"""
简单的奖励权重检查脚本
用途：检查加载checkpoint后的实际奖励权重配置
"""

import argparse
import sys
from distutils.util import strtobool

from isaaclab.app import AppLauncher

parser = argparse.ArgumentParser(description="Check reward weights after loading checkpoint.")
parser.add_argument("--task", type=str, default="Isaac-Leaphand-ContinuousRot-Manager-v0", help="Task name")
parser.add_argument("--checkpoint", type=str, required=True, help="Path to checkpoint file")
parser.add_argument("--num_envs", type=int, default=50, help="Number of environments")
AppLauncher.add_app_launcher_args(parser)

args_cli, hydra_args = parser.parse_known_args()
sys.argv = [sys.argv[0]] + hydra_args

app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

import gymnasium as gym
from isaaclab.envs import ManagerBasedRLEnvCfg
import isaaclab_tasks
from isaaclab_tasks.utils.hydra import hydra_task_config
import leaphand.tasks.manager_based.leaphand  # noqa: F401


def check_curriculum_weights():
    """检查课程学习应该产生的权重"""
    print("\n🎯 根据课程学习配置，23,976,000步时预期的权重:")
    print("-" * 60)
    
    # 根据配置文件分析预期权重
    expected_weights = {
        "rotation_velocity": "15.0 → 40.0 (late阶段)",  # late_step: 120*240*100 = 2,880,000
        "rotation_axis_alignment": "10.0 → 40.0 (late阶段)",  # late_step: 250*240*100 = 6,000,000
        "fingertip_distance_penalty": "-150.0 → -20.0",  # num_steps: 100*240*100 = 2,400,000
        "fall_penalty": "-100.0 → -50.0 (late阶段)",  # late_step: 100*240*100 = 2,400,000
        "action_penalty": "-0.1 → -1.0",  # num_steps: 200*240*100 = 4,800,000
        "pose_diff_penalty": "-1.0 → -0.2",  # num_steps: 200*240*100 = 4,800,000
    }
    
    for term, weight_change in expected_weights.items():
        print(f"  {term:<30} : {weight_change}")
    
    print("-" * 60)


@hydra_task_config(args_cli.task, "rl_games_cfg_entry_point")
def main(env_cfg: ManagerBasedRLEnvCfg, agent_cfg: dict):
    """主函数：创建环境并检查权重"""
    
    print("🔍 奖励权重检查工具")
    print("=" * 50)
    
    # 配置环境
    env_cfg.scene.num_envs = args_cli.num_envs
    env_cfg.sim.device = args_cli.device if args_cli.device is not None else env_cfg.sim.device
    
    # 创建环境（不训练，只检查权重）
    print("\n🏗️  创建环境（仅检查权重，不训练）...")
    env = gym.make(args_cli.task, cfg=env_cfg, render_mode=None)
    
    print("\n📊 环境初始化后的奖励权重:")
    print("-" * 60)
    
    reward_manager = env.unwrapped.reward_manager
    
    for term_name in reward_manager.active_terms:
        term_cfg = reward_manager.get_term_cfg(term_name)
        weight = term_cfg.weight
        print(f"  {term_name:<30} : {weight:>10.4f}")
    
    print("-" * 60)
    
    # 检查步数计数器
    print(f"\n📈 当前环境步数: {env.unwrapped.common_step_counter:,}")
    
    # 模拟一些步骤来看权重是否会变化
    print("\n🚀 运行几个步骤来检查课程学习是否激活...")
    
    # 手动设置步数来测试课程学习（如果环境支持）
    try:
        # 设置一个大的步数来触发课程学习
        env.unwrapped.common_step_counter = 25_000_000
        print(f"✅ 手动设置步数为: {env.unwrapped.common_step_counter:,}")
        
        # 运行一个step来触发课程学习更新
        action = env.action_space.sample()
        env.step(action)
        
        print("\n📊 设置大步数后的奖励权重:")
        print("-" * 60)
        
        for term_name in reward_manager.active_terms:
            term_cfg = reward_manager.get_term_cfg(term_name)
            weight = term_cfg.weight
            print(f"  {term_name:<30} : {weight:>10.4f}")
        
        print("-" * 60)
        
    except Exception as e:
        print(f"⚠️  无法手动设置步数: {e}")
    
    # 显示预期权重
    check_curriculum_weights()
    
    print("\n✅ 权重检查完成")
    env.close()


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ 检查过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        simulation_app.close()
