#!/usr/bin/env python3

# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
测试脚本：验证训练时的实际奖励权重

用途：
1. 加载指定checkpoint
2. 进行短期训练来激活课程学习
3. 检查训练过程中应用的实际权重
4. 验证权重变化
"""

import argparse
import sys
from distutils.util import strtobool

from isaaclab.app import AppLauncher

# 添加命令行参数
parser = argparse.ArgumentParser(description="Test actual reward weights during training.")
parser.add_argument("--task", type=str, default="Isaac-Leaphand-ContinuousRot-Manager-v0", help="Task name")
parser.add_argument("--checkpoint", type=str, required=True, help="Path to checkpoint file")
parser.add_argument("--test_epochs", type=int, default=2, help="Number of epochs to test")
parser.add_argument("--num_envs", type=int, default=50, help="Number of environments")
AppLauncher.add_app_launcher_args(parser)

# 解析参数
args_cli, hydra_args = parser.parse_known_args()
sys.argv = [sys.argv[0]] + hydra_args

# 启动Isaac Sim
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""开始导入其他模块"""

import gymnasium as gym
import torch
import os
from datetime import datetime
from rl_games.common import env_configurations, vecenv
from rl_games.common.algo_observer import IsaacAlgoObserver
from rl_games.torch_runner import Runner

from isaaclab.envs import ManagerBasedRLEnvCfg
from isaaclab.utils.assets import retrieve_file_path

from isaaclab_rl.rl_games import RlGamesGpuEnv, RlGamesVecEnvWrapper
import isaaclab_tasks
from isaaclab_tasks.utils.hydra import hydra_task_config
import leaphand.tasks.manager_based.leaphand  # noqa: F401


def print_section_header(title: str):
    """打印分节标题"""
    print(f"\n{'='*80}")
    print(f"  {title}")
    print(f"{'='*80}")


def print_reward_weights(env, title="当前奖励权重配置"):
    """打印当前环境中所有奖励项的权重"""
    print(f"\n📊 {title}:")
    print("-" * 60)
    
    reward_manager = env.unwrapped.reward_manager
    
    weights_info = {}
    for term_name in reward_manager.active_terms:
        term_cfg = reward_manager.get_term_cfg(term_name)
        weight = term_cfg.weight
        weights_info[term_name] = weight
        print(f"  {term_name:<30} : {weight:>10.4f}")
    
    print("-" * 60)
    return weights_info


def print_step_counter_info(env):
    """打印步数计数器信息"""
    print("\n📈 步数计数器信息:")
    print("-" * 60)
    common_steps = env.unwrapped.common_step_counter
    episode_steps = env.unwrapped.episode_length_buf.max().item()
    print(f"  全局总步数 (common_step_counter)  : {common_steps:>12,}")
    print(f"  最大episode步数                  : {episode_steps:>12,}")
    print("-" * 60)
    return common_steps


def print_expected_weights(common_steps):
    """根据步数计算预期权重"""
    print(f"\n🎯 根据课程学习配置，{common_steps:,}步时预期的权重:")
    print("-" * 60)
    
    # 计算触发阈值
    horizon_length = 240
    num_envs = 100
    
    thresholds = {
        "rotation_velocity_weight": {
            "mid_step": 50 * horizon_length * num_envs,
            "late_step": 120 * horizon_length * num_envs,
            "early_weight": 10.0,
            "mid_weight": 20.0, 
            "late_weight": 40.0
        },
        "rotation_axis_alignment_weight": {
            "mid_step": 100 * horizon_length * num_envs,
            "late_step": 250 * horizon_length * num_envs,
            "early_weight": 10.0,
            "mid_weight": 20.0,
            "late_weight": 40.0
        },
        "fingertip_distance_penalty_weight": {
            "num_steps": 100 * horizon_length * num_envs,
            "early_weight": -150.0,
            "final_weight": -20.0
        },
        "fall_penalty_weight": {
            "mid_step": 20 * horizon_length * num_envs,
            "late_step": 100 * horizon_length * num_envs,
            "early_weight": -200.0,
            "mid_weight": -100.0,
            "late_weight": -50.0
        },
        "action_penalty_weight": {
            "num_steps": 200 * horizon_length * num_envs,
            "early_weight": -0.1,
            "final_weight": -1.0
        },
        "pose_diff_penalty_weight": {
            "num_steps": 200 * horizon_length * num_envs,
            "early_weight": -1.0,
            "final_weight": -0.2
        }
    }
    
    expected = {}
    
    # rotation_velocity
    if common_steps >= thresholds["rotation_velocity_weight"]["late_step"]:
        expected["rotation_velocity"] = thresholds["rotation_velocity_weight"]["late_weight"]
    elif common_steps >= thresholds["rotation_velocity_weight"]["mid_step"]:
        expected["rotation_velocity"] = thresholds["rotation_velocity_weight"]["mid_weight"]
    else:
        expected["rotation_velocity"] = thresholds["rotation_velocity_weight"]["early_weight"]
    
    # rotation_axis_alignment
    if common_steps >= thresholds["rotation_axis_alignment_weight"]["late_step"]:
        expected["rotation_axis_alignment"] = thresholds["rotation_axis_alignment_weight"]["late_weight"]
    elif common_steps >= thresholds["rotation_axis_alignment_weight"]["mid_step"]:
        expected["rotation_axis_alignment"] = thresholds["rotation_axis_alignment_weight"]["mid_weight"]
    else:
        expected["rotation_axis_alignment"] = thresholds["rotation_axis_alignment_weight"]["early_weight"]
    
    # fingertip_distance_penalty
    if common_steps >= thresholds["fingertip_distance_penalty_weight"]["num_steps"]:
        expected["fingertip_distance_penalty"] = thresholds["fingertip_distance_penalty_weight"]["final_weight"]
    else:
        expected["fingertip_distance_penalty"] = thresholds["fingertip_distance_penalty_weight"]["early_weight"]
    
    # fall_penalty
    if common_steps >= thresholds["fall_penalty_weight"]["late_step"]:
        expected["fall_penalty"] = thresholds["fall_penalty_weight"]["late_weight"]
    elif common_steps >= thresholds["fall_penalty_weight"]["mid_step"]:
        expected["fall_penalty"] = thresholds["fall_penalty_weight"]["mid_weight"]
    else:
        expected["fall_penalty"] = thresholds["fall_penalty_weight"]["early_weight"]
    
    # action_penalty
    if common_steps >= thresholds["action_penalty_weight"]["num_steps"]:
        expected["action_penalty"] = thresholds["action_penalty_weight"]["final_weight"]
    else:
        expected["action_penalty"] = thresholds["action_penalty_weight"]["early_weight"]
    
    # pose_diff_penalty
    if common_steps >= thresholds["pose_diff_penalty_weight"]["num_steps"]:
        expected["pose_diff_penalty"] = thresholds["pose_diff_penalty_weight"]["final_weight"]
    else:
        expected["pose_diff_penalty"] = thresholds["pose_diff_penalty_weight"]["early_weight"]
    
    for term, weight in expected.items():
        print(f"  {term:<30} : {weight:>10.1f}")
    
    print("-" * 60)
    return expected


def compare_weights(actual, expected):
    """比较实际权重和预期权重"""
    print("\n🔍 权重对比分析:")
    print("-" * 80)
    print(f"{'奖励项':<30} {'实际权重':<12} {'预期权重':<12} {'状态':<10}")
    print("-" * 80)
    
    all_match = True
    for term in expected.keys():
        actual_weight = actual.get(term, "未找到")
        expected_weight = expected[term]
        
        if isinstance(actual_weight, (int, float)):
            match = abs(actual_weight - expected_weight) < 1e-3
            status = "✅ 匹配" if match else "❌ 不匹配"
            if not match:
                all_match = False
            print(f"{term:<30} {actual_weight:<12.1f} {expected_weight:<12.1f} {status}")
        else:
            print(f"{term:<30} {str(actual_weight):<12} {expected_weight:<12.1f} ❌ 缺失")
            all_match = False
    
    print("-" * 80)
    
    if all_match:
        print("🎉 所有权重都符合预期！")
    else:
        print("⚠️  部分权重不符合预期，可能需要检查课程学习配置。")
    
    return all_match


# 创建自定义观察者来监控权重变化
class WeightObserver(IsaacAlgoObserver):
    def __init__(self):
        super().__init__()
        self.env = None
        self.last_weights = {}
        
    def after_init(self, algo):
        """初始化后获取环境引用"""
        super().after_init(algo)
        if hasattr(algo, 'env') and hasattr(algo.env, 'env'):
            self.env = algo.env.env
            print("\n🔗 观察者已连接到环境")
    
    def after_step(self, algo, step):
        """每步后检查权重变化"""
        if self.env is not None and step % 100 == 0:  # 每100步检查一次
            current_weights = {}
            reward_manager = self.env.reward_manager
            
            for term_name in reward_manager.active_terms:
                term_cfg = reward_manager.get_term_cfg(term_name)
                current_weights[term_name] = term_cfg.weight
            
            # 检查是否有权重变化
            if self.last_weights and current_weights != self.last_weights:
                print(f"\n🔄 步数 {step}: 检测到权重变化!")
                for term, weight in current_weights.items():
                    if term in self.last_weights and self.last_weights[term] != weight:
                        print(f"  {term}: {self.last_weights[term]} → {weight}")
            
            self.last_weights = current_weights.copy()


@hydra_task_config(args_cli.task, "rl_games_cfg_entry_point")
def main(env_cfg: ManagerBasedRLEnvCfg, agent_cfg: dict):
    """主测试函数"""
    
    print_section_header("训练时奖励权重验证测试")
    
    # 配置环境
    env_cfg.scene.num_envs = args_cli.num_envs
    env_cfg.sim.device = args_cli.device if args_cli.device is not None else env_cfg.sim.device
    
    # 配置智能体
    agent_cfg["params"]["seed"] = 42
    agent_cfg["params"]["config"]["max_epochs"] = args_cli.test_epochs
    
    # 修复batch_size兼容性问题
    # batch_size = num_envs * horizon_length = 50 * 240 = 12000
    # 需要找到能被12000整除的minibatch_size
    horizon_length = agent_cfg["params"]["config"]["horizon_length"]
    batch_size = args_cli.num_envs * horizon_length
    
    # 找到合适的minibatch_size (能被batch_size整除)
    original_minibatch = agent_cfg["params"]["config"]["minibatch_size"]
    # 常见的可以被12000整除的数: 100, 120, 125, 150, 200, 240, 250, 300, 400, 480, 500, 600
    suitable_minibatch_sizes = [100, 120, 125, 150, 200, 240, 250, 300, 400, 480, 500, 600]
    for size in suitable_minibatch_sizes:
        if batch_size % size == 0:
            agent_cfg["params"]["config"]["minibatch_size"] = size
            print(f"🔧 调整minibatch_size: {original_minibatch} → {size} (batch_size={batch_size})")
            break
    
    # 配置checkpoint
    if args_cli.checkpoint is not None:
        resume_path = retrieve_file_path(args_cli.checkpoint)
        agent_cfg["params"]["load_checkpoint"] = True
        agent_cfg["params"]["load_path"] = resume_path
        print(f"🔄 加载checkpoint: {resume_path}")
    
    # 设置日志目录
    config_name = agent_cfg["params"]["config"]["name"]
    log_root_path = os.path.join("logs", "rl_games", config_name)
    log_dir = f"test_training_weights_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}"
    agent_cfg["params"]["config"]["train_dir"] = log_root_path
    agent_cfg["params"]["config"]["full_experiment_name"] = log_dir
    
    print(f"📁 测试日志目录: {os.path.join(log_root_path, log_dir)}")
    
    # 创建环境
    print("\n🏗️  创建环境...")
    env = gym.make(args_cli.task, cfg=env_cfg, render_mode=None)
    
    # 包装环境
    clip_obs = agent_cfg["params"]["env"].get("clip_observations", float('inf'))
    clip_actions = agent_cfg["params"]["env"].get("clip_actions", float('inf'))
    rl_device = agent_cfg["params"]["config"]["device"]
    env = RlGamesVecEnvWrapper(env, rl_device, clip_obs, clip_actions)
    
    # 注册环境
    vecenv.register(
        "IsaacRlgWrapper", 
        lambda config_name, num_actors, **kwargs: RlGamesGpuEnv(config_name, num_actors, **kwargs)
    )
    env_configurations.register("rlgpu", {
        "vecenv_type": "IsaacRlgWrapper", 
        "env_creator": lambda **kwargs: env
    })
    
    # 配置智能体
    agent_cfg["params"]["config"]["num_actors"] = env.unwrapped.num_envs
    
    print_section_header("训练前状态检查")
    
    # 创建智能体并加载checkpoint
    print("\n🤖 创建智能体...")
    observer = WeightObserver()
    runner = Runner(observer)
    runner.load(agent_cfg)
    runner.reset()
    
    # 🔧 修复checkpoint步数同步问题
    if args_cli.checkpoint is not None:
        print("\n🔧 同步环境步数计数器...")
        
        # 直接设置已知的checkpoint步数 (23,976,000步)
        # 这是从checkpoint文件路径和训练输出中确认的步数
        checkpoint_steps = 23976000
        original_counter = env.unwrapped.common_step_counter
        env.unwrapped.common_step_counter = checkpoint_steps
        print(f"   环境步数手动同步: {original_counter:,} → {env.unwrapped.common_step_counter:,}")
        
        # 重新计算课程学习权重
        if hasattr(env.unwrapped, 'curriculum_manager') and env.unwrapped.curriculum_manager is not None:
            print("   🎯 重新应用课程学习权重...")
            # 强制更新课程学习
            env.unwrapped.curriculum_manager.compute()
            print("   ✅ 课程学习权重已更新")
    
    # 检查初始状态
    initial_steps = print_step_counter_info(env)
    initial_weights = print_reward_weights(env, "训练前的权重")
    expected_weights = print_expected_weights(initial_steps)
    
    print_section_header("开始训练测试")
    
    print(f"🚀 开始训练 {args_cli.test_epochs} 个epochs...")
    print("   观察权重变化...")
    
    # 运行训练
    if args_cli.checkpoint is not None:
        runner.run({
            "train": True, 
            "play": False, 
            "checkpoint": resume_path
        })
    else:
        runner.run({
            "train": True, 
            "play": False
        })
    
    print_section_header("训练后状态检查")
    
    # 检查训练后状态
    final_steps = print_step_counter_info(env)
    final_weights = print_reward_weights(env, "训练后的权重")
    final_expected = print_expected_weights(final_steps)
    
    # 比较权重
    compare_weights(final_weights, final_expected)
    
    print_section_header("测试结果总结")
    
    print("✅ 测试完成！关键发现:")
    print(f"   • 训练前步数: {initial_steps:,}")
    print(f"   • 训练后步数: {final_steps:,}")
    print(f"   • 新增步数: {final_steps - initial_steps:,}")
    
    # 检查权重是否发生变化
    changed_weights = []
    for term in initial_weights:
        if term in final_weights and abs(initial_weights[term] - final_weights[term]) > 1e-6:
            changed_weights.append(f"{term}: {initial_weights[term]:.3f} → {final_weights[term]:.3f}")
    
    if changed_weights:
        print("   • 权重变化:")
        for change in changed_weights:
            print(f"     - {change}")
    else:
        print("   • 权重未发生变化（可能训练步数不足以触发课程学习）")
    
    # 关闭环境
    env.close()
    print("\n🔚 测试完成，环境已关闭")


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        simulation_app.close()
